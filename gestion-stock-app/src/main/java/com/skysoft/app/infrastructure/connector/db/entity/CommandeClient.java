package com.skysoft.app.infrastructure.connector.db.entity;

import com.skysoft.app.infrastructure.connector.db.entity.enumeration.EtatCommande;
import jakarta.persistence.*;
import java.time.Instant;
import java.util.List;
import lombok.*;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "commandeclient")
public class CommandeClient extends AbstractEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "code")
  private String code;

  @Column(name = "date_commande")
  private Instant dateCommande;

  @Column(name = "etat_commande")
  @Enumerated(EnumType.STRING)
  private EtatCommande etatCommande;

  @ManyToOne
  @JoinColumn(name = "idclient")
  private Client client;

  @OneToMany(fetch = FetchType.LAZY, mappedBy = "commandeClient")
  private List<LigneCommandeClient> ligneCommandeClients;

  @Column(name = "id_entreprise")
  private Long idEntreprise;
}
